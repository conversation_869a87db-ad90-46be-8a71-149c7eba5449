<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">

  <path
      android:pathData="M7,4C5.895,4 5,4.895 5,6L5,20L17,20C18.105,20 19,19.105 19,18L19,4L7,4ZM17.5,5.5L7,5.5Q6.5,5.5 6.5,6L6.5,18.5L17,18.5Q17.5,18.5 17.5,18L17.5,5.5Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="19"
          android:startY="12"
          android:endX="5"
          android:endY="12"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M8,7h8v1.5h-8z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16"
          android:startY="7.75"
          android:endX="8"
          android:endY="7.75"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M8,10h8v1.5h-8z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16"
          android:startY="10.75"
          android:endX="8"
          android:endY="10.75"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M8,13h8v1.5h-8z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16"
          android:startY="13.75"
          android:endX="8"
          android:endY="13.75"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M8,16h4v1.5h-4z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="12"
          android:startY="16.75"
          android:endX="8"
          android:endY="16.75"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
