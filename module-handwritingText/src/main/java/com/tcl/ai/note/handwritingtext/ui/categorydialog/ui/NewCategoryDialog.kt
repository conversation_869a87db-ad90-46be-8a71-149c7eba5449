package com.tcl.ai.note.handwritingtext.ui.categorydialog.ui

import android.annotation.SuppressLint
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogState
import com.tcl.ai.note.handwritingtext.ui.categorydialog.DialogCategory
import com.tcl.ai.note.handwritingtext.ui.utils.rememberKeyboardState
import com.tcl.ai.note.handwritingtext.ui.widget.CustomRadioButton
import com.tcl.ai.note.handwritingtext.utils.isExistCategory
import com.tcl.ai.note.theme.CursorColor
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getCategoryColor
import com.tcl.ai.note.utils.getCategoryColorArray
import com.tcl.ai.note.utils.globalDialogWidth
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.utils.toComposeColor
import com.tcl.ai.note.utils.tryToRequestFocus
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.theme.TclTheme
import kotlinx.coroutines.delay

// 新建分类和重命名弹窗
@OptIn(ExperimentalLayoutApi::class)
@Composable
internal fun NewCategoryDialog(
    dialogCategory: DialogCategory?,
    isNewCategory: Boolean,
    mainFocus: FocusRequester,
    focusManager: FocusManager,
    isSoftKeyboardOpen: Boolean,
    dialogState: CategoryDialogState,
    onConfirm: (String, Int) -> Unit,
    onDismiss: () -> Unit,
    onKeyboardOpen: (Boolean) -> Unit
) {
    // 分类列表数据
    val items by remember(dialogState) {
        derivedStateOf { dialogState.noteCategories }
    }

    val dividerColor = colorResource(R.color.text_field_border)
    var divider by remember { mutableStateOf(dividerColor) }
    var isShowColorPicker by remember { mutableStateOf(false) }
    var isExpandedTip by remember { mutableStateOf(false) }
    val name = if (isNewCategory || dialogCategory == null) "" else dialogCategory.name
    var categoryName by remember { mutableStateOf(name) }

    val categoryId =
        if (isNewCategory || dialogCategory == null) 1L else dialogCategory.categoryId
    val currentColorIndex =
        if (isNewCategory || dialogCategory == null) 0 else dialogCategory.colorIndex
    Logger.i(
        TAG,
        "NewCategoryDialog, dialogCategory:$dialogCategory, name:$name, categoryId:$categoryId, currentColorIndex:$currentColorIndex"
    )
    val context = LocalContext.current
    val density = LocalDensity.current

    val initialColorIndex = if (!isNewCategory) {
        currentColorIndex
    } else {
//        findLeastUsedColor(items) { it.colorIndex } ?: CategoryColors.PINK_COLOR
        CategoryColors.PINK_COLOR
    }
    var selectedColorIndex by remember { mutableIntStateOf(initialColorIndex) }

    val originalColorIndex by remember { mutableIntStateOf(currentColorIndex) }

    Logger.v(
        TAG,
        "NewCategoryDialog, items.size:${items.size}, originalColorIndex:$originalColorIndex, selectedColorIndex:$selectedColorIndex"
    )

    // Compute dialog states for validation
    val clearCategory = categoryName.trim()
    val isNotEmpty = clearCategory.isNotEmpty()
    val isExistCategory =
        isExistCategory(items, clearCategory, categoryId, selectedColorIndex, isNewCategory)
    val isRepeated = isExistCategory && isNotEmpty

    var isOnChanged by remember { mutableStateOf(false) }

    var isMaximumCharacter by remember { mutableStateOf(false) }

    val isDiffColor = selectedColorIndex != originalColorIndex
    val isEnabled = if (isNewCategory) {
        !isExistCategory && isNotEmpty && !isMaximumCharacter
    } else {
        (isDiffColor || !isExistCategory) && isNotEmpty && !isMaximumCharacter
    }
    Logger.v(
        TAG, "NewCategoryDialog, isNotEmpty:$isNotEmpty, isDiffColor:$isDiffColor, " +
                "isExistCategory:$isExistCategory,isMaximumCharacter:$isMaximumCharacter, isEnabled:$isEnabled"
    )
    val dummyFocus = remember { FocusRequester() }



    // 首次弹窗标识
    var isInitialShow by remember { mutableStateOf(true) }

    var nameFieldValue by remember {
        mutableStateOf(
            TextFieldValue(
                text = categoryName,
                selection = TextRange(categoryName.length)
            )
        )
    }
    val isKeyboardVisible = WindowInsets.isImeVisible

    LaunchedEffect(isKeyboardVisible) {
        if (!isKeyboardVisible) {
            mainFocus.freeFocus()
            dummyFocus.requestFocus()
            delay(200)
            Logger.v(TAG, "isKeyboardVisible:$isKeyboardVisible, clearFocus")
            focusManager.clearFocus()
        }else{
            dummyFocus.freeFocus()
            mainFocus.requestFocus()
        }
    }
    Logger.v(TAG, "isKeyboardVisible:$isKeyboardVisible, isInitialShow:$isInitialShow")

    // 智能焦点转移逻辑
    LaunchedEffect(isKeyboardVisible) {
        if (!isKeyboardVisible && !isInitialShow) {
            delay(50)
            divider =
                if (isRepeated && isOnChanged) context.getColor(R.color.text_field_border_error)
                    .toComposeColor() else context.getColor(R.color.text_field_no_focus)
                    .toComposeColor()
//            focusManager.clearFocus()
        } else {
            divider = context.getColor(R.color.text_field_border).toComposeColor()
        }
        isInitialShow = false
    }

    //重命名时移动光标位置至最后（仅在初始化时执行）
    LaunchedEffect(name) {
        nameFieldValue = TextFieldValue(
            text = categoryName,
            selection = TextRange(categoryName.length)
        )
        Logger.d(TAG, "LaunchedEffect, name:$name, nameFieldValue:${nameFieldValue.text}")
    }

    // 请求焦点
    LaunchedEffect(Unit) {
        if (nameFieldValue.text.isEmpty()) {
            mainFocus.requestFocus()
            isInitialShow = true
            Logger.v(TAG, "LaunchedEffect, tryToRequestFocus ")
        }
    }

        var errorTip = stringResource(R.string.category_name_exists)
        TclDialog(
            show = true,
            dialogMaxWidth = globalDialogWidth(),
            title = {
                Text(
                    text = if (isNewCategory)
                        stringResource(R.string.dialog_category_name_title)
                    else
                        stringResource(R.string.dialog_category_rename_title),
                )
            },
            onDismissRequest = onDismiss,
            content = {

                Box {
                    BasicTextField(
                        value = "",
                        onValueChange = { },
                        modifier = Modifier
                            .size(1.dp) // 1像素大小
                            .alpha(0f) // 完全透明
                            .align(Alignment.TopStart) // 放置在左上角
                            .focusRequester(dummyFocus)
                            .onFocusChanged { hasFocus ->
                                Logger.i(
                                    TAG,
                                    "1 size TextField onFocusChanged, hasFocus:${hasFocus.isFocused}"
                                )
                            }
                            .focusable()
                    )

                }

                Column(
                    modifier = Modifier.animateContentSize(
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioLowBouncy,
                            stiffness = Spring.StiffnessLow
                        )
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier
                                .semantics {
                                    contentDescription = context.getString(
                                        isShowColorPicker.judge(
                                            R.string.dialog_category_close_colour_swatches,
                                            R.string.dialog_category_expand_colour_swatches
                                        )
                                    )
                                    role = Role.Button
                                },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_category_pink),
                                contentDescription = null,
                                modifier = Modifier
                                    .width(24.dp)
                                    .height(24.dp),
                                tint = colorResource(getCategoryColor(selectedColorIndex))
                            )
                            val icon = if (isShowColorPicker) {
                                R.drawable.ic_arrow_drop_up
                            } else {
                                R.drawable.ic_arrow_drop_down
                            }

                            HoverProofIconButton(
                                modifier = Modifier.size(24.dp),
                                onClick = { isShowColorPicker = !isShowColorPicker }
                            ) {
                                Icon(
                                    painter = painterResource(id = icon),
                                    contentDescription = isShowColorPicker.judge(
                                        R.string.dialog_category_close_colour_swatches,
                                        R.string.dialog_category_expand_colour_swatches
                                    ).stringRes(),
                                    modifier = Modifier.align(alignment = Alignment.CenterVertically)
                                )
                            }

                            Spacer(modifier = Modifier.width(16.dp))
                        }
                        Row(modifier = Modifier.align(alignment = Alignment.CenterVertically)) {
                            BasicTextField(
                                value = nameFieldValue,
                                onValueChange = { newText ->
                                    // 限制最大输入长度为50个字符
                                    //切换语言时，会导致这里变为true 会导致误判
                                    Logger.i(TAG, "onValueChange, content change")
                                    isOnChanged = true
                                    categoryName = newText.text
                                    nameFieldValue = newText
                                    isMaximumCharacter = newText.text.length > 50
                                },
                                singleLine = true,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .align(Alignment.CenterVertically)
                                    .focusRequester(mainFocus),
                                decorationBox = { innerTextField ->
                                    Column(modifier = Modifier.align(Alignment.CenterVertically)) {
                                        Box(
                                            modifier = Modifier.fillMaxWidth()// 为下划线留出空间
                                        ) {
                                            if (categoryName.isEmpty()) {
                                                Text(
                                                    text = stringResource(R.string.category_name),
                                                    color = colorResource(R.color.text_category_placeholder),
                                                    fontSize = 16.sp,
                                                    lineHeight = with(density) { 22.dp.toSp() },
                                                    overflow = TextOverflow.Ellipsis,
                                                )
                                            }
                                            innerTextField()
                                        }
                                    }
                                },
                                cursorBrush = SolidColor(CursorColor),
                                textStyle = LocalTextStyle.current.copy(
                                    color = colorResource(R.color.text_title),
                                    fontSize = 16.sp,
                                    lineHeight = with(density) { 22.dp.toSp() },
                                ),
                            )
                        }
                    }

                    HorizontalDivider(
                        color = if (isRepeated && isOnChanged) colorResource(R.color.text_field_border_error) else divider,
                        thickness = 2.dp,
                        modifier = Modifier.padding(start = 64.dp)
                    )

                    Spacer(Modifier.height(1.dp))

                    Column(modifier = Modifier.fillMaxWidth()) {
                        // 判断是否需要显示错误提示
                        val showErrorTip = (isRepeated && isOnChanged) || isMaximumCharacter

                        // 更新 isExpandedTip 状态
                        isExpandedTip = showErrorTip

                        if (isMaximumCharacter) {
                            errorTip = context.getString(R.string.category_name_maximum_character)
                        }

                        // 显示错误提示
                        if (showErrorTip) {
                            Text(
                                text = errorTip,
                                color = colorResource(R.color.text_field_border_error),
                                fontSize = 14.sp,
                                lineHeight = with(density) { 16.dp.toSp() },
                                modifier = Modifier.padding(start = 64.dp, top = 6.dp)
                            )
                        }

                        if (isShowColorPicker) {
                            ColorPicker(
                                selectedIndex = selectedColorIndex,
                                onColorSelectedIndex = { index ->
                                    Logger.i(TAG, "onColorSelectedIndex, index:$index")
                                    isOnChanged = true
                                    selectedColorIndex = index
                                },
                            )
                        }
                    }
                }
            },
            actions = {
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ){
                    TclTextButton(
                        onClick = onDismiss,
                        contentColor = colorResource(id = R.color.home_title_color),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = stringResource(R.string.category_cancel),
                            color = colorResource(R.color.btn_new_category_cancel),
                            fontSize = 16.sp
                        )
                    }

                    Spacer(Modifier.width(8.dp))

                    TclTextButton(
                        enabled = isEnabled,
                        onClick = {onConfirm(clearCategory, selectedColorIndex)},
                        contentColor = colorResource(id = R.color.home_title_color),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = stringResource(
                                if (isNewCategory) R.string.category_add else R.string.confirm
                            ),
                            color = if (isEnabled)
                                colorResource(R.color.btn_new_category_cancel)
                            else
                                colorResource(R.color.btn_new_category_create),
                            fontSize = 16.sp
                        )
                    }
                }
            },
        )
}

/**
 * 颜色选择器
 */
@SuppressLint("DesignSystem")
@Composable
internal fun ColorPicker(
    selectedIndex: Int,
    onColorSelectedIndex: (Int) -> Unit,
) {
    //获取颜色列表
    val colors = getCategoryColorArray()
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 1.dp, vertical = 15.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Logger.d("NewCategoryDialog", "selectedIndex:$selectedIndex")
        colors.forEachIndexed { index, color ->
            val curColor = colorResource(color)
            val contentDescription = when (index) {
                0 -> R.string.dialog_category_colour_pink.stringRes()
                1 -> R.string.dialog_category_colour_purple.stringRes()
                2 -> R.string.dialog_category_colour_bluish.stringRes()
                3 -> R.string.dialog_category_colour_green.stringRes()
                4 -> R.string.dialog_category_colour_yellow.stringRes()
                5 -> R.string.dialog_category_colour_orange.stringRes()
                else -> ""
            }

            CustomRadioButton(
                selected = selectedIndex == index,
                onClick = {
                    onColorSelectedIndex(index)
                    Logger.d(
                        "NewCategoryDialog",
                        "onClick(), index:$index, color:$color, curColor:$curColor"
                    )
                },
                color = curColor,
                modifier = Modifier
                    .size(24.dp)
                    .semantics {
                        contentDescription
                    }
            )
        }
    }
}