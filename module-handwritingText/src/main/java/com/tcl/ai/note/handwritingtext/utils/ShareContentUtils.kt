package com.tcl.ai.note.handwritingtext.utils

import android.content.ClipData
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.pdf.PdfDocument
import android.util.TypedValue
import android.view.View.MeasureSpec
import androidx.compose.ui.graphics.toArgb
import androidx.core.content.FileProvider
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.richtext.converter.applyRichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.LINE_HEIGHT_SP
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.TEXT_SIZE_SP
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MESH_CELLS
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.RichTextLayoutUtils.horizontalPaddingPx
import com.tcl.ai.note.handwritingtext.utils.RichTextLayoutUtils.verticalPaddingPx
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.runDefault
import com.tcl.ai.note.utils.runIO
import com.tcl.ai.note.utils.runMain
import com.tcl.ai.note.utils.screenSizeMin
import com.tcl.ai.note.utils.toArgbInt
import com.tcl.ai.note.utils.toComposeColor
import kotlinx.coroutines.coroutineScope
import java.io.File
import java.io.FileOutputStream
import kotlin.math.max
import kotlin.math.roundToInt

object ShareContentUtils {
    private const val TAG = "ShareContentUtils"
    private const val DIR_SHARE = "share"
    private const val SHARE_IMAGE_NAME = "note.png"
    private const val SHARE_PDF_NAME = "Notes.pdf"
    private const val SHARE_HTML_NAME = "Note.html"
    private const val TYPE_IMAGE = "image/png"
    private const val TYPE_PDF = "application/pdf"
    private const val TYPE_HTML = "text/html"
    private val authority = "${GlobalContext.instance.packageName}.fileprovider"
    const val SHARE_MAX_HEIGHT = 10000

    suspend fun saveTextAndDrawToBitmap(
        content: String,
        style: RichTextStyleEntity,
        bgMode: BgMode,
        bgColor: Long,
        isDark: Boolean,
        suniaDrawViewModel: SuniaDrawViewModel,
    ) = coroutineScope {
        Logger.d(TAG, "start to create share image")
        val trimContent = runDefault {
            // 获取最末尾带有样式的下标
            var lastIdx = 0
            sequenceOf(
                style.fontColor,
                style.fontSize,
                style.align,
                style.bold,
                style.italic,
                style.underline,
                style.strikethrough,
                style.backgroundColor,
                style.indent,
                style.list
            ).flatten().forEach {
                lastIdx = max(lastIdx, it.end)
            }
            // 多加了一个换行，所以下标再-1
            if (lastIdx >= 0 && lastIdx < content.length - 1) {
                // 多加一个换行，避免计算错误
                content.take(lastIdx) + content.substring(lastIdx).trimEnd() + '\n'
            } else {
                content
            }
        }
        val textView = runMain {
            AREditText(GlobalContext.instance).apply {
                // 需对齐RichTextViewHolder设置的样式
                setPadding(
                    horizontalPaddingPx,
                    verticalPaddingPx,
                    horizontalPaddingPx,
                    verticalPaddingPx
                )
                textSize = TEXT_SIZE_SP.toFloat()
                setLineHeight(TypedValue.COMPLEX_UNIT_SP, LINE_HEIGHT_SP.toFloat())
                applyRichTextStyleEntity(style, trimContent)
                val widthSpec = MeasureSpec.makeMeasureSpec(screenSizeMin, MeasureSpec.EXACTLY)
                val heightSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
                measure(widthSpec, heightSpec)
                layout(0, 0, screenSizeMin, measuredHeight)
            }
        }
        // 额外添加一个底部padding，避免笔画贴底
        val drawContentBottom =
            suniaDrawViewModel.getScaledContentRange().bottom + verticalPaddingPx
        val maxHeight = max(textView.measuredHeight.toFloat(), drawContentBottom)
        val scale =
            if (maxHeight > SHARE_MAX_HEIGHT)
                SHARE_MAX_HEIGHT / maxHeight
            else 1f
        val targetWidth = (screenSizeMin * scale).roundToInt()
        val targetHeight = (maxHeight * scale).roundToInt()
        Logger.d(TAG, "target share image size: $targetWidth * $targetHeight")
        val bitmap = Bitmap.createBitmap(
            targetWidth,
            targetHeight,
            Bitmap.Config.ARGB_8888
        ).apply {
            // 绘制背景
            eraseColor(
                if (isDark) bgColor.toComposeColor().inverseColor().toArgb()
                else bgColor.toArgbInt()
            )
        }
        runMain {
            val canvas = android.graphics.Canvas(bitmap).apply {
                concat(Matrix().apply {
                    postScale(scale, scale)
                })
            }
            textView.draw(canvas)
        }
        Logger.d(TAG, "draw rich text into share image complete")
        runDefault {
            drawMesh(bitmap, bgMode, bgColor, isDark, scale)
        }
        runIO {
            suniaDrawViewModel.toBitmap(bitmap)
            Logger.d(TAG, "draw sunia draw board into share image complete")
            bitmap
        }
    }

    suspend fun getBitmapFileForShare(bitmap: Bitmap) = runIO {
        val filesDir = GlobalContext.instance.filesDir
        val shareDir = File(filesDir, DIR_SHARE).apply { mkdirs() }
        val outputFile = File(shareDir, SHARE_IMAGE_NAME)
        FileOutputStream(outputFile).use { os ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 80, os)
        }
        outputFile
    }

    suspend fun getPdfFileForShare(bitmap: Bitmap) = runIO {
        val doc = PdfDocument()
        val pageInfo = PdfDocument
            .PageInfo
            .Builder(bitmap.width, bitmap.height, 1)
            .create()
        val page = doc.startPage(pageInfo)
        val canvas = page.canvas
        canvas.drawBitmap(
            bitmap,
            0f,
            0f,
            null
        )
        doc.finishPage(page)
        val shareDir = File(GlobalContext.instance.filesDir, DIR_SHARE).apply { mkdirs() }
        val file = File(shareDir, SHARE_PDF_NAME)
        FileOutputStream(file).use { fos ->
            doc.writeTo(fos)
        }
        file
    }

    suspend fun getHtmlFileForShare(htmlStr: String) = runIO {
        val filesDir = GlobalContext.instance.filesDir
        val shareDir = File(filesDir, DIR_SHARE).apply { mkdirs() }
        val outputFile = File(shareDir, SHARE_HTML_NAME)
        FileOutputStream(outputFile).use { os ->
            os.write(htmlStr.toByteArray())
        }
        outputFile
    }

    fun startImageShareIntent(imageFile: File, context: Context) {
        val contentUri = FileProvider.getUriForFile(
            GlobalContext.instance,
            authority,
            imageFile
        )
        Intent(Intent.ACTION_SEND).apply {
            type = TYPE_IMAGE
            clipData = ClipData.newRawUri("", contentUri)
            putExtra(Intent.EXTRA_STREAM, contentUri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }.also { intent ->
            context.startActivity(
                Intent.createChooser(
                    intent,
                    context.getString(
                        R.string.show_content_share
                    )
                )
            )
        }
    }

    fun startPdfShareIntent(pdfFile: File, context: Context) {
        val contentUri = FileProvider.getUriForFile(
            GlobalContext.instance,
            authority,
            pdfFile
        )
        Intent(Intent.ACTION_SEND).apply {
            type = TYPE_PDF
            putExtra(Intent.EXTRA_STREAM, contentUri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }.also { intent ->
            context.startActivity(
                Intent.createChooser(
                    intent,
                    context.getString(
                        R.string.show_content_share
                    )
                )
            )
        }
    }

    fun startHtmlShareIntent(htmlFile: File, context: Context) {
        val contentUri = FileProvider.getUriForFile(
            GlobalContext.instance,
            authority,
            htmlFile
        )
        Intent(Intent.ACTION_SEND).apply {
            type = TYPE_HTML
            putExtra(Intent.EXTRA_STREAM, contentUri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }.also { intent ->
            context.startActivity(
                Intent.createChooser(
                    intent,
                    context.getString(
                        R.string.show_content_share
                    )
                )
            )
        }
    }

    // 注意需要对齐MeshStyle中对背景的定义
    private fun drawMesh(
        bitmap: Bitmap,
        bgMode: BgMode,
        bgColor: Long,
        isDark: Boolean,
        scale: Float = 1f
    ) {
        if (bgMode == BgMode.none) return
        val height = bitmap.height / scale
        val width = bitmap.width / scale
        val space = screenSizeMin / MESH_CELLS
        val paddingTop = 0f
        val paint = Paint().apply {
            strokeWidth = 1.dp2px.toFloat()
            color = (isDark && bgColor == Skin.defColor).judge(
                0xFF2A2A2A.toInt(),
                0x7FCCCCCC,
            )
            style = Paint.Style.STROKE
        }
        android.graphics.Canvas(bitmap).apply {
            concat(Matrix().apply {
                postScale(scale, scale)
            })
            var y = paddingTop
            while (y < height) {
                if (y != 0f) {
                    drawLine(
                        0f,
                        y,
                        width,
                        y,
                        paint,
                    )
                }
                y += space
            }
            if (bgMode == BgMode.grid) {
                var x = 0f
                while (x <= width) {
                    drawLine(
                        x,
                        paddingTop,
                        x,
                        height,
                        paint,
                    )
                    x += space
                }
            }
        }
    }
}